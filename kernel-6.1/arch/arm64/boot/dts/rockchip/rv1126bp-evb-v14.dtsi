/* DTS-XLATE-RV1126B */
// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 */

/ {
	rk809_sound: rk809-sound {
		compatible = "simple-audio-card";
		simple-audio-card,format = "i2s";
		simple-audio-card,name = "rockchip,rk809-codec";
		simple-audio-card,mclk-fs = <256>;
		simple-audio-card,cpu {
			sound-dai = <&sai0>;
		};
		simple-audio-card,codec {
			sound-dai = <&rk809_codec>;
		};
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_enable_h>;

		/*
		 * On the module itself this is one of these (depending
		 * on the actual card populated):
		 * - SDIO_RESET_L_WL_REG_ON
		 * - PDN (power down when low)
		 */
		reset-gpios = <&gpio0 RK_PB2 GPIO_ACTIVE_LOW>;
	};

	vcc_mipi: vcc-mipi {
		compatible = "regulator-fixed";
		regulator-name = "vcc_mipi";
		gpio = <&gpio6 RK_PA3 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		regulator-boot-on;
	};

	vcc_sys: vcc-sys {
		compatible = "regulator-fixed";
		regulator-name = "vcc_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <3800000>;
		regulator-max-microvolt = <3800000>;
	};

	vcc5v0_sys: vcc5v0-sys {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&vcc_sys>;
	};

	vdd_log: vdd-log {
		compatible = "regulator-fixed";
		regulator-name = "vdd_log";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <900000>;
		regulator-max-microvolt = <900000>;
		vin-supply = <&vcc5v0_sys>;
	};

	wireless-bluetooth {
		compatible = "bluetooth-platdata";
		uart_rts_gpios = <&gpio6 RK_PA2 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default", "rts_gpio";
		pinctrl-0 = <&uart2m0_rtsn_pins_pins>;
		pinctrl-1 = <&uart2_gpios>;
		BT,power_gpio = <&gpio0 RK_PC0 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	wireless-wlan {
		compatible = "wlan-platdata";
		rockchip,grf = <&grf>;
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_wake_host>;
		wifi_chip_type = "rk96x";
		WIFI,host_wake_irq = <&gpio0 RK_PC1 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};
};

&backlight {
	pwms = <&pwm0_8ch_0 0 25000 0>;
};

&cpu0 {
	cpu-supply = <&vdd_arm>;
};

&display_subsystem {
	status = "okay";
};

&dsi {
	status = "okay";
};

&dsi_in_vop {
	status = "okay";
};

&dsi_panel {
	power-supply = <&vcc_mipi>;
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	non-removable;
	mmc-hs200-1_8v;
	rockchip,default-sample-phase = <90>;
	no-sdio;
	no-sd;
	status = "okay";
};

&gmac {
	phy-mode = "rgmii";
	clock_in_out = "output";

	snps,reset-gpio = <&gpio5 RK_PD4 GPIO_ACTIVE_LOW>;
	snps,reset-active-low;
	/* Reset time is 20ms, 100ms for rtl8211f */
	snps,reset-delays-us = <0 20000 100000>;

	assigned-clocks = <&cru CLK_MAC_OUT2IO>;
	assigned-clock-rates = <25000000>;

	pinctrl-names = "default";
	pinctrl-0 = <&ethm1_miim_pins
		     &ethm1_tx_bus2_pins
		     &ethm1_rx_bus2_pins
		     &ethm1_rgmii_clk_pins
		     &ethm1_rgmii_bus_pins
		     &eth_clk_25mm1_out_pins>;

	tx_delay = <0x36>;
	rx_delay = <0x2c>;

	phy-handle = <&rgmii_phy>;
	status = "okay";
};

&i2c0 {
	status = "okay";

	rk809: pmic@20 {
		compatible = "rockchip,rk809";
		reg = <0x20>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PC0 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default", "pmic-sleep",
				"pmic-power-off";
		pinctrl-0 = <&pmic_int>;
		pinctrl-1 = <&soc_slppin_gpio>, <&rk817_slppin_slp>;
		pinctrl-2 = <&soc_slppin_gpio>, <&rk817_slppin_pwrdn>;
		rockchip,system-power-controller;
		wakeup-source;
		#clock-cells = <1>;
		clock-output-names = "rk808-clkout2";
		//fb-inner-reg-idxs = <2>;
		/* 1: rst regs (default in codes), 0: rst the pmic */
		pmic-reset-func = <1>;

		vcc1-supply = <&vcc5v0_sys>;
		vcc2-supply = <&vcc5v0_sys>;
		vcc3-supply = <&vcc5v0_sys>;
		vcc4-supply = <&vcc5v0_sys>;
		vcc5-supply = <&vcc5v0_sys>;
		vcc6-supply = <&vcc5v0_sys>;
		vcc7-supply = <&vcc5v0_sys>;
		vcc8-supply = <&vcc3v3_sys>;
		vcc9-supply = <&vcc5v0_sys>;

		pwrkey {
			status = "okay";
		};

		pinctrl_rk8xx: pinctrl_rk8xx {
			gpio-controller;
			#gpio-cells = <2>;

			rk817_slppin_null: rk817_slppin_null {
				pins = "gpio_slp";
				function = "pin_fun0";
			};

			rk817_slppin_slp: rk817_slppin_slp {
				pins = "gpio_slp";
				function = "pin_fun1";
			};

			rk817_slppin_pwrdn: rk817_slppin_pwrdn {
				pins = "gpio_slp";
				function = "pin_fun2";
			};

			rk817_slppin_rst: rk817_slppin_rst {
				pins = "gpio_slp";
				function = "pin_fun3";
			};
		};

		regulators {
			vdd_npu: vdd_epu: DCDC_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1350000>;
				regulator-ramp-delay = <6001>;
				regulator-initial-mode = <0x2>;
				regulator-name = "vdd_npu";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <950000>;
				};
			};

			vdd_arm: DCDC_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1350000>;
				regulator-ramp-delay = <6001>;
				regulator-initial-mode = <0x2>;
				regulator-name = "vdd_arm";
				regulator-state-mem {
					regulator-off-in-suspend;
					regulator-suspend-microvolt = <800000>;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-initial-mode = <0x2>;
				regulator-name = "vcc_ddr";
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc3v3_sys: DCDC_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-initial-mode = <0x2>;
				regulator-name = "vcc3v3_sys";
				regulator-state-mem {
					regulator-off-in-suspend;
					regulator-suspend-microvolt = <3000000>;
				};
			};

			vcc_0v8: LDO_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <800000>;
				regulator-name = "vcc_0v8";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <800000>;
				};
			};

			vcc1v8_pmu: LDO_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc1v8_pmu";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vdd0v8_pmu: LDO_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <800000>;
				regulator-name = "vcc0v8_pmu";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <800000>;
				};
			};

			vcc_1v8: LDO_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc_1v8";
				regulator-state-mem {
					regulator-off-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc_dovdd: LDO_REG5 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc_dovdd";
				regulator-state-mem {
					regulator-off-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc_dvdd: LDO_REG6 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-name = "vcc_dvdd";
				regulator-state-mem {
					regulator-off-in-suspend;
					regulator-suspend-microvolt = <1200000>;
				};
			};

			vcc_avdd: LDO_REG7 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-name = "vcc_avdd";
				regulator-state-mem {
					regulator-off-in-suspend;
					regulator-suspend-microvolt = <2800000>;
				};
			};

			vccio_sd: LDO_REG8 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vccio_sd";
				regulator-state-mem {
					regulator-off-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcc3v3_sd: LDO_REG9 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcc3v3_sd";
				regulator-state-mem {
					regulator-off-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcc_buck5: DCDC_REG5 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <2200000>;
				regulator-max-microvolt = <2200000>;
				regulator-name = "vcc_buck5";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <2200000>;
				};
			};

			vcc5v0_host: SWITCH_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vcc5v0_host";
			};

			vcc_3v3: SWITCH_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vcc_3v3";
			};
		};

		rk809_codec: codec {
			#sound-dai-cells = <0>;
			compatible = "rockchip,rk809-codec", "rockchip,rk817-codec";
			clocks = <&sai0_mclkout>;
			clock-names = "mclk";
			assigned-clocks = <&sai0_mclkout>;
			assigned-clock-rates = <12288000>;
			pinctrl-names = "default";
			pinctrl-0 = <&sai0m0_mclk_pins>;
			hp-volume = <20>;
			spk-volume = <3>;
		};
	};
};

&i2c5 {
	pinctrl-0 = <&i2c5m2_pins>;
	status = "okay";

	gt1x: gt1x@14 {
		compatible = "goodix,gt1x";
		reg = <0x14>;
		pinctrl-names = "default";
		gtp_ics_slot_report;
		power-supply = <&vcc_mipi>;
		goodix,rst-gpio = <&gpio5 RK_PA4 GPIO_ACTIVE_HIGH>;
		goodix,irq-gpio = <&gpio5 RK_PA6 IRQ_TYPE_LEVEL_LOW>;
	};
};

&mdio {
	rgmii_phy: phy@1 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <0x1>;
		clocks = <&cru CLK_MAC_OUT2IO>;
	};
};

&mipi_dphy {
	status = "okay";
};

&pinctrl {
	pmic {
		pmic_int: pmic_int {
			rockchip,pins =
				<0 RK_PC6 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		soc_slppin_gpio: soc_slppin_gpio {
			rockchip,pins =
				<0 RK_PC7 RK_FUNC_GPIO &pcfg_output_low>;
		};
	};

	sdio-pwrseq {
		wifi_enable_h: wifi-enable-h {
			rockchip,pins = <0 RK_PB2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	wireless-bluetooth {
		uart2_gpios: uart2-gpios {
			rockchip,pins = <6 RK_PA2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	wireless-wlan {
		wifi_wake_host: wifi-wake-host {
			rockchip,pins = <0 RK_PC1 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};
};

&pwm0_8ch_0 {
	status = "okay";
};

&rkaiisp {
	status = "okay";
};

&rkaiisp_mmu {
	status = "okay";
};

&rkaiisp_vir0 {
	status = "okay";
};

&rknpu {
	rknpu-supply = <&vdd_npu>;
};

&route_dsi {
	status = "okay";
};

&sai0 {
	status = "okay";
	rockchip,sai-rx-route = <3 1 2 0>;
	pinctrl-names = "default";
	pinctrl-0 = <&sai0m0_lrck_pins
		     &sai0m0_sclk_pins
		     &sai0m0_sdi3_pins
		     &sai0m0_sdo0_pins>;
};

&sai0_mclkout {
	status = "okay";
};

&saradc0 {
	vref-supply = <&vcc_1v8>;
};

&sdmmc0 {
	max-frequency = <200000000>;
	no-sdio;
	no-mmc;
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	disable-wp;
	sd-uhs-sdr104;
	vmmc-supply = <&vcc3v3_sd>;
	vqmmc-supply = <&vccio_sd>;
	status = "okay";
};

&sdmmc1 {
	bus-width = <4>;
	cap-sd-highspeed;
	no-sd;
	no-mmc;
	max-frequency = <200000000>;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc1_clk_pins &sdmmc1_cmd_pins &sdmmc1_bus4_pins>;
	keep-power-in-suspend;
	non-removable;
	mmc-pwrseq = <&sdio_pwrseq>;
	sd-uhs-sdr104;
	status = "okay";
};

// &uart0 {
// 	status = "okay";
// 	pinctrl-names = "default";
// 	pinctrl-0 = <&uart0m0_xfer_pins &uart0m0_ctsn_pins>;
// };

&usb2phy_host {
	phy-supply = <&vcc5v0_host>;
};

&usb_drd_dwc3 {
	extcon = <&usb2phy>;
	phys = <&usb2phy_otg>;
	phy-names = "usb2-phy";
	maximum-speed = "high-speed";
	snps,dis_u2_susphy_quirk;
	snps,usb2-lpm-disable;
	snps,usb2-gadget-lpm-disable;
};